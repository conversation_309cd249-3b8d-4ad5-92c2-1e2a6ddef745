import * as revenueStatistics from '@/services/revenue-statistics';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Col, DatePicker, Row, Tabs, message } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';
import CompositionChart from './components/CompositionChart';
import EmployeeStats from './components/EmployeeStats';
import ServiceStats from './components/ServiceStats';
import StatisticsCards from './components/StatisticsCards';
import TrendChart from './components/TrendChart';

const { RangePicker } = DatePicker;

const IncomeAnalysis: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [overviewData, setOverviewData] = useState<API.RevenueOverviewStats>();

  // 日期范围状态，默认近半年
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(6, 'month'),
    dayjs(),
  ]);

  // 获取概览数据（传递时间参数）
  const fetchOverviewData = async () => {
    try {
      setLoading(true);
      const params = {
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      };

      console.log('Fetching overview data with params:', params);

      const { errCode, msg, data } = await revenueStatistics.overview(params);

      if (errCode) {
        message.error(msg || '获取收入概览数据失败');
        return;
      }

      if (data) {
        console.log('Overview data received:', data);
        setOverviewData(data);
      }
    } catch (error) {
      console.error('获取收入概览数据失败:', error);
      message.error('获取收入概览数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOverviewData();
  }, []);

  // 监听时间范围变化
  useEffect(() => {
    console.log('Date range changed in Income Analysis:', dateRange);
    fetchOverviewData();
  }, [dateRange]);

  // 处理日期范围变化
  const handleDateRangeChange = (dates: any) => {
    console.log('=== Income Analysis RangePicker onChange 触发 ===');
    console.log('Selected dates:', dates);
    if (dates) {
      const newDateRange = dates as [Dayjs, Dayjs];
      console.log('Setting new dateRange:', newDateRange);
      console.log('New start date:', newDateRange[0].format('YYYY-MM-DD'));
      console.log('New end date:', newDateRange[1].format('YYYY-MM-DD'));
      setDateRange(newDateRange);
    }
  };

  // Tab页签配置
  const tabItems = [
    {
      key: 'overview',
      label: '数据概览',
      children: (
        <div>
          {/* 统计卡片区域 */}
          <StatisticsCards data={overviewData} loading={loading} />

          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24}>
              <CompositionChart dateRange={dateRange} />
            </Col>
          </Row>

          {/* 图表分析区域 */}
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24}>
              <TrendChart dateRange={dateRange} />
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: 'service',
      label: '服务统计',
      children: <ServiceStats dateRange={dateRange} />,
    },
    {
      key: 'employee',
      label: '员工统计',
      children: <EmployeeStats dateRange={dateRange} />,
    },
  ];

  return (
    <PageContainer
      title="收入数据分析"
      subTitle="收入统计与分析"
      breadcrumb={{ items: [] }}
      extra={[
        <RangePicker
          key="dateRange"
          value={dateRange}
          onChange={handleDateRangeChange}
          allowClear={false}
          format="YYYY-MM-DD"
        />,
      ]}
    >
      <Card>
        <Tabs items={tabItems} destroyInactiveTabPane />
      </Card>
    </PageContainer>
  );
};

export default IncomeAnalysis;
