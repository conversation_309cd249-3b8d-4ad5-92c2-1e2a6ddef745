import { Descriptions, Image, Modal, Rate, Space, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

const { Paragraph } = Typography;

interface ReviewDetailModalProps {
  visible: boolean;
  review: ReviewData | null;
  onClose: () => void;
}

const ReviewDetailModal: React.FC<ReviewDetailModalProps> = ({
  visible,
  review,
  onClose,
}) => {
  if (!review) return null;

  // 解析图片URLs
  let photoUrls: string[] = [];

  // 调试信息
  console.log('ReviewDetailModal - review.photoURLs:', review.photoURLs);
  console.log('ReviewDetailModal - photoURLs type:', typeof review.photoURLs);

  if (review.photoURLs) {
    if (Array.isArray(review.photoURLs)) {
      // 如果是数组，直接使用
      photoUrls = review.photoURLs.filter((url) => url && url.trim());
    } else if (typeof review.photoURLs === 'string') {
      // 如果是字符串，尝试解析为JSON数组
      try {
        const parsed = JSON.parse(review.photoURLs);
        if (Array.isArray(parsed)) {
          photoUrls = parsed.filter((url) => url && url.trim());
        } else {
          // 如果不是数组，可能是单个URL
          photoUrls = [review.photoURLs].filter(
            (url) => url && (url as string).trim(),
          );
        }
      } catch (e) {
        // 解析失败，可能是逗号分隔的字符串
        photoUrls = (review.photoURLs as string)
          .split(',')
          .map((url) => url.trim())
          .filter(Boolean);
      }
    }
  }

  console.log('ReviewDetailModal - processed photoUrls:', photoUrls);

  return (
    <Modal
      title="评价详情"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Descriptions bordered column={2} size="small">
        <Descriptions.Item label="评价ID" span={1}>
          {review.id}
        </Descriptions.Item>
        <Descriptions.Item label="订单编号" span={1}>
          {review.order?.sn || '未知订单'}
        </Descriptions.Item>

        <Descriptions.Item label="客户姓名" span={1}>
          {review.customer?.nickname || '未知客户'}
        </Descriptions.Item>
        <Descriptions.Item label="客户电话" span={1}>
          {review.customer?.phone || '未知电话'}
        </Descriptions.Item>

        <Descriptions.Item label="服务员工" span={1}>
          {review.order?.employee?.name || '未知员工'}
        </Descriptions.Item>
        <Descriptions.Item label="员工等级" span={1}>
          <Tag color="blue">等级 {review.order?.employee?.level || 0}</Tag>
        </Descriptions.Item>

        <Descriptions.Item label="评分" span={1}>
          <Space>
            <Rate disabled value={review.rating} />
            <span>{review.rating} 分</span>
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="评价时间" span={1}>
          {review.createdAt
            ? dayjs(review.createdAt).format('YYYY-MM-DD HH:mm:ss')
            : '未知时间'}
        </Descriptions.Item>

        <Descriptions.Item label="评价内容" span={2}>
          <Paragraph>{review.comment || '无文字评价'}</Paragraph>
        </Descriptions.Item>

        {photoUrls.length > 0 && (
          <Descriptions.Item label="评价图片" span={2}>
            <Space wrap>
              {photoUrls.map((url, index) => {
                // 处理URL格式
                let imageUrl = url.trim();

                // 如果URL不是以http开头，添加协议
                if (
                  imageUrl &&
                  !imageUrl.startsWith('http') &&
                  !imageUrl.startsWith('//')
                ) {
                  imageUrl = '//' + imageUrl;
                }

                console.log(`Image ${index + 1} URL:`, imageUrl);

                return (
                  <Image
                    key={index}
                    width={100}
                    height={100}
                    src={imageUrl}
                    style={{ objectFit: 'cover', borderRadius: '4px' }}
                    placeholder={
                      <div
                        style={{
                          width: 100,
                          height: 100,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backgroundColor: '#f5f5f5',
                          borderRadius: '4px',
                        }}
                      >
                        加载中...
                      </div>
                    }
                    fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                    onError={(e) => {
                      console.error(
                        `Failed to load image ${index + 1}:`,
                        imageUrl,
                        e,
                      );
                    }}
                  />
                );
              })}
            </Space>
          </Descriptions.Item>
        )}
      </Descriptions>
    </Modal>
  );
};

export default ReviewDetailModal;
