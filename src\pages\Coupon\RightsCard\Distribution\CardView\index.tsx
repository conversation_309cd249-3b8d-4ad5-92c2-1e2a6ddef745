import { getUsersByCardType } from '@/services/customer-membership-cards';
import { index } from '@/services/membership-card-types';
import { formatNumber } from '@/utils/format';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Drawer, message, Space, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import UserList from './UserList';

/**
 * 权益卡视角组件
 */
const CardView: React.FC = () => {
  // 表格操作引用
  const actionRef = useRef<ActionType>();

  // 抽屉状态
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [currentCardId, setCurrentCardId] = useState<number>(0);
  const [currentCardName, setCurrentCardName] = useState<string>('');

  // 获取权益卡发放数量
  const [cardCounts, setCardCounts] = useState<Record<number, number>>({});

  // 加载权益卡发放数量
  const loadCardCounts = async (cards: API.MembershipCardType[]) => {
    const counts: Record<number, number> = {};

    // 并行获取每个权益卡的发放数量
    await Promise.all(
      cards.map(async (card) => {
        try {
          const response = await getUsersByCardType(card.id, {
            current: 1,
            pageSize: 1,
          });
          if (!response.errCode) {
            counts[card.id] = response.data?.total || 0;
          }
        } catch (error) {
          console.error(`获取权益卡 ${card.id} 的发放数量失败`, error);
        }
      }),
    );

    setCardCounts(counts);
  };

  // 处理查看用户列表
  const handleViewUsers = (record: API.MembershipCardType) => {
    setCurrentCardId(record.id);
    setCurrentCardName(record.name);
    setDrawerVisible(true);
  };

  // 处理发放数量变化
  const handleCountChange = () => {
    // 重新加载当前权益卡的发放数量
    if (currentCardId) {
      loadCardCounts([{ id: currentCardId } as API.MembershipCardType]);
    }
  };

  // 表格列定义
  const columns: ProColumns<API.MembershipCardType>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '权益卡名称',
      dataIndex: 'name',
      ellipsis: true,
      width: 150,
    },
    {
      title: '售价',
      dataIndex: 'price',
      hideInSearch: true,
      width: 100,
      render: (_, record) => `¥${record.price}`,
    },
    {
      title: '有效期',
      dataIndex: 'validDays',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.validDays ? `${record.validDays}天` : '无限期',
    },
    {
      title: '折扣率',
      dataIndex: 'discountRate',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.discountRate
          ? `${formatNumber(record.discountRate * 10, 1)}折`
          : '-',
    },
    {
      title: '可用次数',
      dataIndex: 'usageLimit',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.usageLimit && record.usageLimit >= 0
          ? `${record.usageLimit}次`
          : '不限',
    },
    {
      title: '发放数量',
      dataIndex: 'userCount',
      hideInSearch: true,
      width: 100,
      render: (_, record) => {
        // 使用动态获取的发放数量
        const count = cardCounts[record.id] || 0;
        return <Tag color="blue">{count}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'isEnabled',
      width: 100,
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Error' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button type="link" onClick={() => handleViewUsers(record)}>
            管理
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.MembershipCardType>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={false}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        scroll={{ x: 1200 }}
        request={async (params) => {
          const response = await index({
            ...params,
          });

          if (response.errCode) {
            message.error(response.msg || '获取权益卡类型列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          // 获取权益卡列表后，加载每个权益卡的发放数量
          const cards = response.data?.list || [];
          if (cards.length > 0) {
            loadCardCounts(cards);
          }

          return {
            data: cards,
            success: true,
            total: response.data?.total || 0,
          };
        }}
      />

      {/* 用户列表抽屉 */}
      <Drawer
        title={`${currentCardName} - 发放用户列表`}
        width={1300}
        placement="right"
        onClose={() => {
          setCurrentCardId(0);
          setCurrentCardName('');
          setDrawerVisible(false);
        }}
        open={drawerVisible}
        destroyOnClose={true}
      >
        <UserList cardId={currentCardId} onCountChange={handleCountChange} />
      </Drawer>
    </>
  );
};

export default CardView;
