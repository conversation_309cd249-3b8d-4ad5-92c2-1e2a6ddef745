import {
  CategoryConfig,
  ComplaintCategory,
  ComplaintStatus,
  ComplaintSubCategory,
  SourceType,
  SourceTypeConfig,
  StatusConfig,
  SubCategoryConfig,
} from '@/constants/complaint';
import { complaints } from '@/services';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import {
  Button,
  message,
  Popconfirm,
  Space,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import React, { useRef, useState } from 'react';
import ComplaintCreateModal from './components/ComplaintCreateModal';
import ComplaintDetailDrawer from './components/ComplaintDetailDrawer';
import ComplaintHandleModal from './components/ComplaintHandleModal';
import ComplaintHistoryModal from './components/ComplaintHistoryModal';
import ProcessGuideModal from './components/ProcessGuideModal';

const { Text } = Typography;

const ComplaintManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [detailVisible, setDetailVisible] = useState(false);
  const [handleVisible, setHandleVisible] = useState(false);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [processGuideVisible, setProcessGuideVisible] = useState(false);
  const [createVisible, setCreateVisible] = useState(false);
  const [currentComplaint, setCurrentComplaint] = useState<API.Complaint>();

  /** 查看详情 */
  const handleViewDetail = (record: API.Complaint) => {
    setCurrentComplaint(record);
    setDetailVisible(true);
  };

  /** 处理投诉建议 */
  const handleProcess = (record: API.Complaint) => {
    setCurrentComplaint(record);
    setHandleVisible(true);
  };

  /** 查看处理流程 */
  const handleViewHistory = (record: API.Complaint) => {
    setCurrentComplaint(record);
    setHistoryVisible(true);
  };

  /** 删除投诉建议 */
  const handleDelete = async (record: API.Complaint) => {
    const response = await complaints.remove(record.id);
    if (response.errCode) {
      message.error(response.msg || '删除失败');
    } else {
      message.success('删除成功');
      actionRef.current?.reload();
    }
  };

  /** 处理投诉建议提交 */
  const handleProcessSubmit = async (values: {
    status: API.ComplaintStatus;
    result?: string;
    handlerId: number;
  }) => {
    if (!currentComplaint) return;

    const response = await complaints.handle(currentComplaint.id, values);
    if (response.errCode) {
      message.error(response.msg || '处理失败');
    } else {
      message.success('处理成功');
      setHandleVisible(false);
      actionRef.current?.reload();
    }
  };

  /** 获取状态标签 */
  const getStatusTag = (status: API.ComplaintStatus) => {
    const config = StatusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  /** 获取分类标签 */
  const getCategoryTag = (
    category: API.ComplaintCategory,
    subCategory: API.ComplaintSubCategory,
  ) => {
    const categoryInfo = CategoryConfig[category];
    const subCategoryText = SubCategoryConfig[subCategory];

    return (
      <Space>
        <Tag color={categoryInfo.color}>{categoryInfo.text}</Tag>
        <Text type="secondary">{subCategoryText}</Text>
      </Space>
    );
  };

  /** 获取来源标签 */
  const getSourceTag = (complaint: API.Complaint) => {
    // 根据接口文档的逻辑判断来源类型
    let sourceType: API.SourceType;
    let sourceName: string;

    if (complaint.sourceType) {
      // 如果接口返回了来源类型，直接使用
      sourceType = complaint.sourceType;
      sourceName = complaint.sourceName || '';
    } else {
      // 兼容历史数据，根据字段判断
      if (complaint.customerId) {
        sourceType = SourceType.客户投诉建议 as API.SourceType;
        sourceName = complaint.customer?.nickname || '客户';
      } else if (complaint.employeeId && !complaint.customerId) {
        sourceType = SourceType.员工建议 as API.SourceType;
        sourceName = complaint.employee?.name || '员工';
      } else if (complaint.createdBy) {
        sourceType = SourceType.管理员录入 as API.SourceType;
        sourceName = '管理员录入';
      } else {
        sourceType = SourceType.未知 as API.SourceType;
        sourceName = '未知来源';
      }
    }

    const config = SourceTypeConfig[sourceType];

    // 特殊处理未知来源
    if (sourceType === SourceType.未知 || sourceName === '未知' || sourceName === '未知来源') {
      return (
        <Space direction="vertical" size={0}>
          <Tag color={config.color}>
            {config.icon} {config.text}
          </Tag>
          <Tooltip title={config.description}>
            <Text type="secondary" style={{ fontSize: 12, color: '#ff4d4f' }}>
              未知来源
            </Text>
          </Tooltip>
        </Space>
      );
    }

    return (
      <Space direction="vertical" size={0}>
        <Tag color={config.color}>
          {config.icon} {config.text}
        </Tag>
        <Text type="secondary" style={{ fontSize: 12 }}>
          {sourceName}
        </Text>
      </Space>
    );
  };

  const columns: ProColumns<API.Complaint>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '标题',
      dataIndex: 'title',
      width: 200,
      ellipsis: true,
      render: (text, record) => (
        <Button type="link" onClick={() => handleViewDetail(record)}>
          {text}
        </Button>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      width: 150,
      valueType: 'select',
      valueEnum: {
        [ComplaintCategory.投诉]: { text: '投诉' },
        [ComplaintCategory.建议]: { text: '建议' },
      },
      render: (_, record) =>
        getCategoryTag(record.category, record.subCategory),
    },
    {
      title: '小类',
      dataIndex: 'subCategory',
      width: 120,
      valueType: 'select',
      valueEnum: {
        [ComplaintSubCategory.订单投诉]: { text: '订单投诉' },
        [ComplaintSubCategory.人员投诉]: { text: '人员投诉' },
        [ComplaintSubCategory.平台建议]: { text: '平台建议' },
        [ComplaintSubCategory.服务建议]: { text: '服务建议' },
        [ComplaintSubCategory.流程投诉]: { text: '流程投诉' },
      },
      search: false,
    },
    {
      title: '来源',
      width: 150,
      search: false,
      render: (_, record) => getSourceTag(record),
    },
    {
      title: '来源类型',
      dataIndex: 'sourceType',
      width: 120,
      valueType: 'select',
      valueEnum: {
        [SourceType.客户投诉建议]: { text: '客户投诉建议' },
        [SourceType.员工建议]: { text: '员工建议' },
        [SourceType.管理员录入]: { text: '管理员录入' },
      },
      hideInTable: true, // 只在搜索中显示，表格中用"来源"列展示
    },
    {
      title: '关联信息',
      width: 120,
      search: false,
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          {record.orderId && (
            <Text style={{ fontSize: 12 }}>订单: {record.orderId}</Text>
          )}
          {record.employeeId && (
            <Text style={{ fontSize: 12 }}>员工: {record.employeeId}</Text>
          )}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: {
        [ComplaintStatus.待处理]: { text: '待处理' },
        [ComplaintStatus.处理中]: { text: '处理中' },
        [ComplaintStatus.已解决]: { text: '已解决' },
        [ComplaintStatus.已关闭]: { text: '已关闭' },
      },
      render: (_, record) => getStatusTag(record.status),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 280,
      render: (_, record) =>
        [
          <Button
            key="detail"
            type="link"
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            查看详情
          </Button>,
          <Button
            key="history"
            type="link"
            size="small"
            onClick={() => handleViewHistory(record)}
          >
            查看流程
          </Button>,
          record.status === ComplaintStatus.待处理 ||
          record.status === ComplaintStatus.处理中 ? (
            <Button
              key="handle"
              type="link"
              size="small"
              onClick={() => handleProcess(record)}
            >
              处理
            </Button>
          ) : null,
          record.status === ComplaintStatus.待处理 ? (
            <Popconfirm
              key="delete"
              title="确定要删除这条投诉建议吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" size="small" danger>
                删除
              </Button>
            </Popconfirm>
          ) : null,
        ].filter(Boolean),
    },
  ];

  return (
    <>
      <ProTable<API.Complaint>
        actionRef={actionRef}
        rowKey="id"
        headerTitle={
          <Space>
            投诉建议管理
            <Tooltip title="点击查看处理流程说明">
              <QuestionCircleOutlined
                style={{ color: '#1890ff', cursor: 'pointer' }}
                onClick={() => setProcessGuideVisible(true)}
              />
            </Tooltip>
          </Space>
        }
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
        scroll={{ x: 1400 }}
        request={async (params, sort) => {
          const { errCode, msg, data } = await complaints.index({
            ...params,
            ...sort,
          });
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
            success: true,
          };
        }}
        toolBarRender={() => [
          <Button
            key="create"
            type="primary"
            onClick={() => setCreateVisible(true)}
          >
            录入投诉
          </Button>,
          <Button key="refresh" onClick={() => actionRef.current?.reload()}>
            刷新
          </Button>,
        ]}
      />

      {/* 详情抽屉 */}
      <ComplaintDetailDrawer
        visible={detailVisible}
        complaint={currentComplaint}
        onClose={() => {
          setDetailVisible(false);
          setCurrentComplaint(undefined);
        }}
      />

      {/* 处理模态框 */}
      <ComplaintHandleModal
        visible={handleVisible}
        complaint={currentComplaint}
        onSubmit={handleProcessSubmit}
        onClose={() => {
          setHandleVisible(false);
          setCurrentComplaint(undefined);
        }}
      />

      {/* 流程说明模态框 */}
      <ProcessGuideModal
        visible={processGuideVisible}
        onClose={() => setProcessGuideVisible(false)}
      />

      {/* 录入投诉模态框 */}
      <ComplaintCreateModal
        visible={createVisible}
        onClose={() => setCreateVisible(false)}
        onSuccess={() => {
          setCreateVisible(false);
          actionRef.current?.reload();
        }}
      />

      {/* 处理历史模态框 */}
      <ComplaintHistoryModal
        visible={historyVisible}
        complaintId={currentComplaint?.id}
        onClose={() => {
          setHistoryVisible(false);
          setCurrentComplaint(undefined);
        }}
      />
    </>
  );
};

export default ComplaintManagement;
