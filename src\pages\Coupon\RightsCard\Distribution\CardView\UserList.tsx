import {
  getUsersByCardType,
  remove,
} from '@/services/customer-membership-cards';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space } from 'antd';
import React, { useRef, useState } from 'react';
import IssueCardModal from '../components/IssueCardModal';

interface UserListProps {
  cardId: number;
  onCountChange?: () => void; // 发放数量变化时的回调
}

/**
 * 权益卡用户列表组件
 */
const UserList: React.FC<UserListProps> = ({ cardId, onCountChange }) => {
  const actionRef = useRef<ActionType>();

  // 发放权益卡模态框状态
  const [issueModalVisible, setIssueModalVisible] = useState<boolean>(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<number>(0);

  // 处理撤销发放
  const handleRevoke = async (id: number) => {
    try {
      const response = await remove(id);
      if (response.errCode) {
        message.error(response.msg || '撤销发放失败');
      } else {
        message.success('撤销发放成功');
        actionRef.current?.reload();
        // 通知父组件刷新发放数量
        onCountChange?.();
      }
    } catch (error) {
      console.error('撤销发放失败', error);
      message.error('撤销发放失败，请重试');
    }
  };

  // 表格列定义
  const columns: ProColumns<API.CustomerMembershipCard>[] = [
    {
      title: '用户ID',
      dataIndex: ['customer', 'id'],
      hideInSearch: true,
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: ['customer', 'nickname'],
      ellipsis: true,
      width: 120,
    },
    {
      title: '手机号',
      dataIndex: ['customer', 'phone'],
      width: 120,
    },
    {
      title: '注册时间',
      dataIndex: ['customer', 'createdAt'],
      hideInSearch: true,
      width: 180,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '购买时间',
      dataIndex: 'purchaseTime',
      hideInSearch: true,
      width: 180,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '到期时间',
      dataIndex: 'expiryTime',
      hideInSearch: true,
      width: 180,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '剩余次数',
      dataIndex: 'remainingUses',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.remainTimes === undefined || record.remainTimes === -1
          ? '不限'
          : record.remainTimes,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        active: { text: '有效', status: 'Success' },
        expired: { text: '已失效', status: 'Error' },
        used: { text: '已用完', status: 'Warning' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Popconfirm
            title="确定要撤销此权益卡吗？"
            description="撤销后将无法恢复，用户将无法使用此权益卡"
            onConfirm={() => handleRevoke(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger>
              撤销发放
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.CustomerMembershipCard>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        options={false}
        request={async (params) => {
          if (!cardId) {
            return {
              data: [],
              success: true,
              total: 0,
            };
          }

          const response = await getUsersByCardType(cardId, params);

          if (response.errCode) {
            message.error(response.msg || '获取用户列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
        toolBarRender={() => [
          <Button
            key="issue"
            type="primary"
            onClick={() => {
              setSelectedCustomerId(0);
              setIssueModalVisible(true);
            }}
          >
            批量发放权益卡
          </Button>,
        ]}
      />

      {/* 发放权益卡模态框 */}
      <IssueCardModal
        open={issueModalVisible}
        onClose={() => setIssueModalVisible(false)}
        onSuccess={() => {
          setIssueModalVisible(false);
          actionRef.current?.reload();
          // 通知父组件刷新发放数量
          onCountChange?.();
        }}
        cardTypeId={cardId}
        customerId={selectedCustomerId}
      />
    </>
  );
};

export default UserList;
