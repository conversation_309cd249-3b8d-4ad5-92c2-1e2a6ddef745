import { defineConfig } from '@umijs/max';
import proxy from './proxy';
import routes from './routes';

export default defineConfig({
  antd: {
    configProvider: {},
  },
  access: {},
  model: {},
  initialState: {},
  request: {},
  dva: {},
  styledComponents: {},
  layout: {
    title: '贝宠管理平台',
  },
  define: {
    uploadType: 'cos', // 上传类型，cos: 腾讯云对象存储，zos: 天翼云对象存储
    // 腾讯云对象存储配置
    COS_CONFIG: {
      Region: 'ap-guangzhou',
      Bucket: 'pet-**********',
      Prefix: '',
    },
    // 天翼云对象存储配置
    ZOS_CONFIG: {
      accessKeyId: 'Q725Z5R4YHZBADGG54L9',
      secretAccessKey: 'TCqBAoLp0FEcuSsRWf6jxHL6licfHTAwcVxyaECF',
      endpoint: 'xian7.zos.ctyun.cn',
      Bucket: 'pet',
    },
    // 高德地图配置
    AMAP_CONFIG: {
      key: '60b9934af1d9787dfff1725c8494d55e', // 高德地图 API Key
      securityJsCode: 'f40b7371e64d91c3c2fb78b9d03531db', // 高德地图安全密钥
    },
    // 不支持二级域名时，需要配置base区分不同应用，这里也要保持一致，去掉后面的/，这个变量用于sso跳转和其他无法使用useLocation时的路由加工
    // BASE_URL: '/pet',
  },
  // base: '/pet/',
  routes: routes as any,
  favicons: ['/logo.png'],
  plugins: ['./plugins/add_zos_js.ts', './plugins/service-index-generator.ts'],
  hash: true,
  npmClient: 'pnpm',
  // proxy: proxy('http://127.0.0.1:3001'),
  proxy: proxy('https://manager.petsjoylife.com/api'),
});
